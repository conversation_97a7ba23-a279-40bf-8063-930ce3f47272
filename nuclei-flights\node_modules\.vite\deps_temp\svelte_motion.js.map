{"version": 3, "sources": ["../../svelte/motion/index.mjs"], "sourcesContent": ["import { writable } from '../store/index.mjs';\nimport { now, loop, assign } from '../internal/index.mjs';\nimport { linear } from '../easing/index.mjs';\n\nfunction is_date(obj) {\n    return Object.prototype.toString.call(obj) === '[object Date]';\n}\n\nfunction tick_spring(ctx, last_value, current_value, target_value) {\n    if (typeof current_value === 'number' || is_date(current_value)) {\n        // @ts-ignore\n        const delta = target_value - current_value;\n        // @ts-ignore\n        const velocity = (current_value - last_value) / (ctx.dt || 1 / 60); // guard div by 0\n        const spring = ctx.opts.stiffness * delta;\n        const damper = ctx.opts.damping * velocity;\n        const acceleration = (spring - damper) * ctx.inv_mass;\n        const d = (velocity + acceleration) * ctx.dt;\n        if (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {\n            return target_value; // settled\n        }\n        else {\n            ctx.settled = false; // signal loop to keep ticking\n            // @ts-ignore\n            return is_date(current_value) ?\n                new Date(current_value.getTime() + d) : current_value + d;\n        }\n    }\n    else if (Array.isArray(current_value)) {\n        // @ts-ignore\n        return current_value.map((_, i) => tick_spring(ctx, last_value[i], current_value[i], target_value[i]));\n    }\n    else if (typeof current_value === 'object') {\n        const next_value = {};\n        for (const k in current_value) {\n            // @ts-ignore\n            next_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);\n        }\n        // @ts-ignore\n        return next_value;\n    }\n    else {\n        throw new Error(`Cannot spring ${typeof current_value} values`);\n    }\n}\nfunction spring(value, opts = {}) {\n    const store = writable(value);\n    const { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;\n    let last_time;\n    let task;\n    let current_token;\n    let last_value = value;\n    let target_value = value;\n    let inv_mass = 1;\n    let inv_mass_recovery_rate = 0;\n    let cancel_task = false;\n    function set(new_value, opts = {}) {\n        target_value = new_value;\n        const token = current_token = {};\n        if (value == null || opts.hard || (spring.stiffness >= 1 && spring.damping >= 1)) {\n            cancel_task = true; // cancel any running animation\n            last_time = now();\n            last_value = new_value;\n            store.set(value = target_value);\n            return Promise.resolve();\n        }\n        else if (opts.soft) {\n            const rate = opts.soft === true ? .5 : +opts.soft;\n            inv_mass_recovery_rate = 1 / (rate * 60);\n            inv_mass = 0; // infinite mass, unaffected by spring forces\n        }\n        if (!task) {\n            last_time = now();\n            cancel_task = false;\n            task = loop(now => {\n                if (cancel_task) {\n                    cancel_task = false;\n                    task = null;\n                    return false;\n                }\n                inv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);\n                const ctx = {\n                    inv_mass,\n                    opts: spring,\n                    settled: true,\n                    dt: (now - last_time) * 60 / 1000\n                };\n                const next_value = tick_spring(ctx, last_value, value, target_value);\n                last_time = now;\n                last_value = value;\n                store.set(value = next_value);\n                if (ctx.settled) {\n                    task = null;\n                }\n                return !ctx.settled;\n            });\n        }\n        return new Promise(fulfil => {\n            task.promise.then(() => {\n                if (token === current_token)\n                    fulfil();\n            });\n        });\n    }\n    const spring = {\n        set,\n        update: (fn, opts) => set(fn(target_value, value), opts),\n        subscribe: store.subscribe,\n        stiffness,\n        damping,\n        precision\n    };\n    return spring;\n}\n\nfunction get_interpolator(a, b) {\n    if (a === b || a !== a)\n        return () => a;\n    const type = typeof a;\n    if (type !== typeof b || Array.isArray(a) !== Array.isArray(b)) {\n        throw new Error('Cannot interpolate values of different type');\n    }\n    if (Array.isArray(a)) {\n        const arr = b.map((bi, i) => {\n            return get_interpolator(a[i], bi);\n        });\n        return t => arr.map(fn => fn(t));\n    }\n    if (type === 'object') {\n        if (!a || !b)\n            throw new Error('Object cannot be null');\n        if (is_date(a) && is_date(b)) {\n            a = a.getTime();\n            b = b.getTime();\n            const delta = b - a;\n            return t => new Date(a + t * delta);\n        }\n        const keys = Object.keys(b);\n        const interpolators = {};\n        keys.forEach(key => {\n            interpolators[key] = get_interpolator(a[key], b[key]);\n        });\n        return t => {\n            const result = {};\n            keys.forEach(key => {\n                result[key] = interpolators[key](t);\n            });\n            return result;\n        };\n    }\n    if (type === 'number') {\n        const delta = b - a;\n        return t => a + t * delta;\n    }\n    throw new Error(`Cannot interpolate ${type} values`);\n}\nfunction tweened(value, defaults = {}) {\n    const store = writable(value);\n    let task;\n    let target_value = value;\n    function set(new_value, opts) {\n        if (value == null) {\n            store.set(value = new_value);\n            return Promise.resolve();\n        }\n        target_value = new_value;\n        let previous_task = task;\n        let started = false;\n        let { delay = 0, duration = 400, easing = linear, interpolate = get_interpolator } = assign(assign({}, defaults), opts);\n        if (duration === 0) {\n            if (previous_task) {\n                previous_task.abort();\n                previous_task = null;\n            }\n            store.set(value = target_value);\n            return Promise.resolve();\n        }\n        const start = now() + delay;\n        let fn;\n        task = loop(now => {\n            if (now < start)\n                return true;\n            if (!started) {\n                fn = interpolate(value, new_value);\n                if (typeof duration === 'function')\n                    duration = duration(value, new_value);\n                started = true;\n            }\n            if (previous_task) {\n                previous_task.abort();\n                previous_task = null;\n            }\n            const elapsed = now - start;\n            if (elapsed > duration) {\n                store.set(value = new_value);\n                return false;\n            }\n            // @ts-ignore\n            store.set(value = fn(easing(elapsed / duration)));\n            return true;\n        });\n        return task.promise;\n    }\n    return {\n        set,\n        update: (fn, opts) => set(fn(target_value, value), opts),\n        subscribe: store.subscribe\n    };\n}\n\nexport { spring, tweened };\n"], "mappings": ";;;;;;;;;;;;;AAIA,SAAS,QAAQ,KAAK;AAClB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACnD;AAEA,SAAS,YAAY,KAAK,YAAY,eAAe,cAAc;AAC/D,MAAI,OAAO,kBAAkB,YAAY,QAAQ,aAAa,GAAG;AAE7D,UAAM,QAAQ,eAAe;AAE7B,UAAM,YAAY,gBAAgB,eAAe,IAAI,MAAM,IAAI;AAC/D,UAAMA,UAAS,IAAI,KAAK,YAAY;AACpC,UAAM,SAAS,IAAI,KAAK,UAAU;AAClC,UAAM,gBAAgBA,UAAS,UAAU,IAAI;AAC7C,UAAM,KAAK,WAAW,gBAAgB,IAAI;AAC1C,QAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW;AAC1E,aAAO;AAAA,IACX,OACK;AACD,UAAI,UAAU;AAEd,aAAO,QAAQ,aAAa,IACxB,IAAI,KAAK,cAAc,QAAQ,IAAI,CAAC,IAAI,gBAAgB;AAAA,IAChE;AAAA,EACJ,WACS,MAAM,QAAQ,aAAa,GAAG;AAEnC,WAAO,cAAc,IAAI,CAAC,GAAG,MAAM,YAAY,KAAK,WAAW,IAAI,cAAc,IAAI,aAAa,EAAE,CAAC;AAAA,EACzG,WACS,OAAO,kBAAkB,UAAU;AACxC,UAAM,aAAa,CAAC;AACpB,eAAW,KAAK,eAAe;AAE3B,iBAAW,KAAK,YAAY,KAAK,WAAW,IAAI,cAAc,IAAI,aAAa,EAAE;AAAA,IACrF;AAEA,WAAO;AAAA,EACX,OACK;AACD,UAAM,IAAI,MAAM,iBAAiB,OAAO,sBAAsB;AAAA,EAClE;AACJ;AACA,SAAS,OAAO,OAAO,OAAO,CAAC,GAAG;AAC9B,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAE,YAAY,MAAM,UAAU,KAAK,YAAY,KAAK,IAAI;AAC9D,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,MAAI,WAAW;AACf,MAAI,yBAAyB;AAC7B,MAAI,cAAc;AAClB,WAAS,IAAI,WAAWC,QAAO,CAAC,GAAG;AAC/B,mBAAe;AACf,UAAM,QAAQ,gBAAgB,CAAC;AAC/B,QAAI,SAAS,QAAQA,MAAK,QAASD,QAAO,aAAa,KAAKA,QAAO,WAAW,GAAI;AAC9E,oBAAc;AACd,kBAAY,IAAI;AAChB,mBAAa;AACb,YAAM,IAAI,QAAQ,YAAY;AAC9B,aAAO,QAAQ,QAAQ;AAAA,IAC3B,WACSC,MAAK,MAAM;AAChB,YAAM,OAAOA,MAAK,SAAS,OAAO,MAAK,CAACA,MAAK;AAC7C,+BAAyB,KAAK,OAAO;AACrC,iBAAW;AAAA,IACf;AACA,QAAI,CAAC,MAAM;AACP,kBAAY,IAAI;AAChB,oBAAc;AACd,aAAO,KAAK,CAAAC,SAAO;AACf,YAAI,aAAa;AACb,wBAAc;AACd,iBAAO;AACP,iBAAO;AAAA,QACX;AACA,mBAAW,KAAK,IAAI,WAAW,wBAAwB,CAAC;AACxD,cAAM,MAAM;AAAA,UACR;AAAA,UACA,MAAMF;AAAA,UACN,SAAS;AAAA,UACT,KAAKE,OAAM,aAAa,KAAK;AAAA,QACjC;AACA,cAAM,aAAa,YAAY,KAAK,YAAY,OAAO,YAAY;AACnE,oBAAYA;AACZ,qBAAa;AACb,cAAM,IAAI,QAAQ,UAAU;AAC5B,YAAI,IAAI,SAAS;AACb,iBAAO;AAAA,QACX;AACA,eAAO,CAAC,IAAI;AAAA,MAChB,CAAC;AAAA,IACL;AACA,WAAO,IAAI,QAAQ,YAAU;AACzB,WAAK,QAAQ,KAAK,MAAM;AACpB,YAAI,UAAU;AACV,iBAAO;AAAA,MACf,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACA,QAAMF,UAAS;AAAA,IACX;AAAA,IACA,QAAQ,CAAC,IAAIC,UAAS,IAAI,GAAG,cAAc,KAAK,GAAGA,KAAI;AAAA,IACvD,WAAW,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,SAAOD;AACX;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO,MAAM;AACjB,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,OAAO,KAAK,MAAM,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG;AAC5D,UAAM,IAAI,MAAM,6CAA6C;AAAA,EACjE;AACA,MAAI,MAAM,QAAQ,CAAC,GAAG;AAClB,UAAM,MAAM,EAAE,IAAI,CAAC,IAAI,MAAM;AACzB,aAAO,iBAAiB,EAAE,IAAI,EAAE;AAAA,IACpC,CAAC;AACD,WAAO,OAAK,IAAI,IAAI,QAAM,GAAG,CAAC,CAAC;AAAA,EACnC;AACA,MAAI,SAAS,UAAU;AACnB,QAAI,CAAC,KAAK,CAAC;AACP,YAAM,IAAI,MAAM,uBAAuB;AAC3C,QAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC1B,UAAI,EAAE,QAAQ;AACd,UAAI,EAAE,QAAQ;AACd,YAAM,QAAQ,IAAI;AAClB,aAAO,OAAK,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACtC;AACA,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,UAAM,gBAAgB,CAAC;AACvB,SAAK,QAAQ,SAAO;AAChB,oBAAc,OAAO,iBAAiB,EAAE,MAAM,EAAE,IAAI;AAAA,IACxD,CAAC;AACD,WAAO,OAAK;AACR,YAAM,SAAS,CAAC;AAChB,WAAK,QAAQ,SAAO;AAChB,eAAO,OAAO,cAAc,KAAK,CAAC;AAAA,MACtC,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,SAAS,UAAU;AACnB,UAAM,QAAQ,IAAI;AAClB,WAAO,OAAK,IAAI,IAAI;AAAA,EACxB;AACA,QAAM,IAAI,MAAM,sBAAsB,aAAa;AACvD;AACA,SAAS,QAAQ,OAAO,WAAW,CAAC,GAAG;AACnC,QAAM,QAAQ,SAAS,KAAK;AAC5B,MAAI;AACJ,MAAI,eAAe;AACnB,WAAS,IAAI,WAAW,MAAM;AAC1B,QAAI,SAAS,MAAM;AACf,YAAM,IAAI,QAAQ,SAAS;AAC3B,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,mBAAe;AACf,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACd,QAAI,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAQ,cAAc,iBAAiB,IAAI,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,IAAI;AACtH,QAAI,aAAa,GAAG;AAChB,UAAI,eAAe;AACf,sBAAc,MAAM;AACpB,wBAAgB;AAAA,MACpB;AACA,YAAM,IAAI,QAAQ,YAAY;AAC9B,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,UAAM,QAAQ,IAAI,IAAI;AACtB,QAAI;AACJ,WAAO,KAAK,CAAAE,SAAO;AACf,UAAIA,OAAM;AACN,eAAO;AACX,UAAI,CAAC,SAAS;AACV,aAAK,YAAY,OAAO,SAAS;AACjC,YAAI,OAAO,aAAa;AACpB,qBAAW,SAAS,OAAO,SAAS;AACxC,kBAAU;AAAA,MACd;AACA,UAAI,eAAe;AACf,sBAAc,MAAM;AACpB,wBAAgB;AAAA,MACpB;AACA,YAAM,UAAUA,OAAM;AACtB,UAAI,UAAU,UAAU;AACpB,cAAM,IAAI,QAAQ,SAAS;AAC3B,eAAO;AAAA,MACX;AAEA,YAAM,IAAI,QAAQ,GAAG,OAAO,UAAU,QAAQ,CAAC,CAAC;AAChD,aAAO;AAAA,IACX,CAAC;AACD,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,CAAC,IAAI,SAAS,IAAI,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,IACvD,WAAW,MAAM;AAAA,EACrB;AACJ;", "names": ["spring", "opts", "now"]}