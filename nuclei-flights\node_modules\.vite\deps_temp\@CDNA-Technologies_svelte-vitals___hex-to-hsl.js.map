{"version": 3, "sources": ["../../rgb-to-hsl/index.js", "../../hex-to-rgb/index.js", "../../hex-to-hsl/index.js"], "sourcesContent": ["(function() {\n  module.exports = function(r, g, b) {\n    var d, h, l, max, min, s;\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    max = Math.max(r, g, b);\n    min = Math.min(r, g, b);\n    h = 0;\n    s = 0;\n    l = (max + min) / 2;\n    if (max === min) {\n      h = s = 0;\n    } else {\n      d = max - min;\n      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n      }\n      h /= 6;\n    }\n    h = Math.ceil(h * 360);\n    s = (Math.ceil(s * 100)) + \"%\";\n    l = (Math.ceil(l * 100)) + \"%\";\n    return [h, s, l];\n  };\n\n}).call(this);\n", "module.exports = function hexToRgb (hex) {\n\n  if (hex.charAt && hex.charAt(0) === '#') {\n    hex = removeHash(hex)\n  }\n\n  if (hex.length === 3) {\n    hex = expand(hex)\n  }\n\n  var bigint = parseInt(hex, 16)\n  var r = (bigint >> 16) & 255\n  var g = (bigint >> 8) & 255\n  var b = bigint & 255\n\n  return [r, g, b]\n}\n\nfunction removeHash (hex) {\n\n  var arr = hex.split('')\n  arr.shift()\n  return arr.join('')\n}\n\nfunction expand (hex) {\n\n  return hex\n    .split('')\n    .reduce(function (accum, value) {\n\n      return accum.concat([value, value])\n    }, [])\n    .join('')\n}\n", "\nvar rgbToHsl = require('rgb-to-hsl');\nvar hexToRgb = require('hex-to-rgb');\n\nmodule.exports = function (hex) {\n\tvar hsl = rgbToHsl.apply(rgbToHsl, hexToRgb(hex));\n\treturn [hsl[0], parseInt(hsl[1], 10), parseInt(hsl[2], 10)];\n};\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,WAAW;AACV,aAAO,UAAU,SAAS,GAAG,GAAG,GAAG;AACjC,YAAI,GAAG,GAAG,GAAG,KAAK,KAAK;AACvB,aAAK;AACL,aAAK;AACL,aAAK;AACL,cAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACtB,cAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACtB,YAAI;AACJ,YAAI;AACJ,aAAK,MAAM,OAAO;AAClB,YAAI,QAAQ,KAAK;AACf,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,MAAM;AACV,cAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,kBAAQ,KAAK;AAAA,YACX,KAAK;AACH,mBAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,YACF,KAAK;AACH,mBAAK,IAAI,KAAK,IAAI;AAClB;AAAA,YACF,KAAK;AACH,mBAAK,IAAI,KAAK,IAAI;AAAA,UACtB;AACA,eAAK;AAAA,QACP;AACA,YAAI,KAAK,KAAK,IAAI,GAAG;AACrB,YAAK,KAAK,KAAK,IAAI,GAAG,IAAK;AAC3B,YAAK,KAAK,KAAK,IAAI,GAAG,IAAK;AAC3B,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB;AAAA,IAEF,GAAG,KAAK,OAAI;AAAA;AAAA;;;AClCZ;AAAA;AAAA,WAAO,UAAU,SAAS,SAAU,KAAK;AAEvC,UAAI,IAAI,UAAU,IAAI,OAAO,CAAC,MAAM,KAAK;AACvC,cAAM,WAAW,GAAG;AAAA,MACtB;AAEA,UAAI,IAAI,WAAW,GAAG;AACpB,cAAM,OAAO,GAAG;AAAA,MAClB;AAEA,UAAI,SAAS,SAAS,KAAK,EAAE;AAC7B,UAAI,IAAK,UAAU,KAAM;AACzB,UAAI,IAAK,UAAU,IAAK;AACxB,UAAI,IAAI,SAAS;AAEjB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IACjB;AAEA,aAAS,WAAY,KAAK;AAExB,UAAI,MAAM,IAAI,MAAM,EAAE;AACtB,UAAI,MAAM;AACV,aAAO,IAAI,KAAK,EAAE;AAAA,IACpB;AAEA,aAAS,OAAQ,KAAK;AAEpB,aAAO,IACJ,MAAM,EAAE,EACR,OAAO,SAAU,OAAO,OAAO;AAE9B,eAAO,MAAM,OAAO,CAAC,OAAO,KAAK,CAAC;AAAA,MACpC,GAAG,CAAC,CAAC,EACJ,KAAK,EAAE;AAAA,IACZ;AAAA;AAAA;;;AClCA;AAAA;AACA,QAAI,WAAW;AACf,QAAI,WAAW;AAEf,WAAO,UAAU,SAAU,KAAK;AAC/B,UAAI,MAAM,SAAS,MAAM,UAAU,SAAS,GAAG,CAAC;AAChD,aAAO,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC;AAAA,IAC3D;AAAA;AAAA;", "names": []}