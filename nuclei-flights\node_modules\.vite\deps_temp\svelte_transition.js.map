{"version": 3, "sources": ["../../svelte/transition/index.mjs"], "sourcesContent": ["import { cubicInOut, linear, cubicOut } from '../easing/index.mjs';\nimport { split_css_unit, is_function, assign } from '../internal/index.mjs';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nfunction blur(node, { delay = 0, duration = 400, easing = cubicInOut, amount = 5, opacity = 0 } = {}) {\n    const style = getComputedStyle(node);\n    const target_opacity = +style.opacity;\n    const f = style.filter === 'none' ? '' : style.filter;\n    const od = target_opacity * (1 - opacity);\n    const [value, unit] = split_css_unit(amount);\n    return {\n        delay,\n        duration,\n        easing,\n        css: (_t, u) => `opacity: ${target_opacity - (od * u)}; filter: ${f} blur(${u * value}${unit});`\n    };\n}\nfunction fade(node, { delay = 0, duration = 400, easing = linear } = {}) {\n    const o = +getComputedStyle(node).opacity;\n    return {\n        delay,\n        duration,\n        easing,\n        css: t => `opacity: ${t * o}`\n    };\n}\nfunction fly(node, { delay = 0, duration = 400, easing = cubicOut, x = 0, y = 0, opacity = 0 } = {}) {\n    const style = getComputedStyle(node);\n    const target_opacity = +style.opacity;\n    const transform = style.transform === 'none' ? '' : style.transform;\n    const od = target_opacity * (1 - opacity);\n    const [xValue, xUnit] = split_css_unit(x);\n    const [yValue, yUnit] = split_css_unit(y);\n    return {\n        delay,\n        duration,\n        easing,\n        css: (t, u) => `\n\t\t\ttransform: ${transform} translate(${(1 - t) * xValue}${xUnit}, ${(1 - t) * yValue}${yUnit});\n\t\t\topacity: ${target_opacity - (od * u)}`\n    };\n}\nfunction slide(node, { delay = 0, duration = 400, easing = cubicOut, axis = 'y' } = {}) {\n    const style = getComputedStyle(node);\n    const opacity = +style.opacity;\n    const primary_property = axis === 'y' ? 'height' : 'width';\n    const primary_property_value = parseFloat(style[primary_property]);\n    const secondary_properties = axis === 'y' ? ['top', 'bottom'] : ['left', 'right'];\n    const capitalized_secondary_properties = secondary_properties.map((e) => `${e[0].toUpperCase()}${e.slice(1)}`);\n    const padding_start_value = parseFloat(style[`padding${capitalized_secondary_properties[0]}`]);\n    const padding_end_value = parseFloat(style[`padding${capitalized_secondary_properties[1]}`]);\n    const margin_start_value = parseFloat(style[`margin${capitalized_secondary_properties[0]}`]);\n    const margin_end_value = parseFloat(style[`margin${capitalized_secondary_properties[1]}`]);\n    const border_width_start_value = parseFloat(style[`border${capitalized_secondary_properties[0]}Width`]);\n    const border_width_end_value = parseFloat(style[`border${capitalized_secondary_properties[1]}Width`]);\n    return {\n        delay,\n        duration,\n        easing,\n        css: t => 'overflow: hidden;' +\n            `opacity: ${Math.min(t * 20, 1) * opacity};` +\n            `${primary_property}: ${t * primary_property_value}px;` +\n            `padding-${secondary_properties[0]}: ${t * padding_start_value}px;` +\n            `padding-${secondary_properties[1]}: ${t * padding_end_value}px;` +\n            `margin-${secondary_properties[0]}: ${t * margin_start_value}px;` +\n            `margin-${secondary_properties[1]}: ${t * margin_end_value}px;` +\n            `border-${secondary_properties[0]}-width: ${t * border_width_start_value}px;` +\n            `border-${secondary_properties[1]}-width: ${t * border_width_end_value}px;`\n    };\n}\nfunction scale(node, { delay = 0, duration = 400, easing = cubicOut, start = 0, opacity = 0 } = {}) {\n    const style = getComputedStyle(node);\n    const target_opacity = +style.opacity;\n    const transform = style.transform === 'none' ? '' : style.transform;\n    const sd = 1 - start;\n    const od = target_opacity * (1 - opacity);\n    return {\n        delay,\n        duration,\n        easing,\n        css: (_t, u) => `\n\t\t\ttransform: ${transform} scale(${1 - (sd * u)});\n\t\t\topacity: ${target_opacity - (od * u)}\n\t\t`\n    };\n}\nfunction draw(node, { delay = 0, speed, duration, easing = cubicInOut } = {}) {\n    let len = node.getTotalLength();\n    const style = getComputedStyle(node);\n    if (style.strokeLinecap !== 'butt') {\n        len += parseInt(style.strokeWidth);\n    }\n    if (duration === undefined) {\n        if (speed === undefined) {\n            duration = 800;\n        }\n        else {\n            duration = len / speed;\n        }\n    }\n    else if (typeof duration === 'function') {\n        duration = duration(len);\n    }\n    return {\n        delay,\n        duration,\n        easing,\n        css: (_, u) => `\n\t\t\tstroke-dasharray: ${len};\n\t\t\tstroke-dashoffset: ${u * len};\n\t\t`\n    };\n}\nfunction crossfade(_a) {\n    var { fallback } = _a, defaults = __rest(_a, [\"fallback\"]);\n    const to_receive = new Map();\n    const to_send = new Map();\n    function crossfade(from_node, node, params) {\n        const { delay = 0, duration = d => Math.sqrt(d) * 30, easing = cubicOut } = assign(assign({}, defaults), params);\n        const from = from_node.getBoundingClientRect();\n        const to = node.getBoundingClientRect();\n        const dx = from.left - to.left;\n        const dy = from.top - to.top;\n        const dw = from.width / to.width;\n        const dh = from.height / to.height;\n        const d = Math.sqrt(dx * dx + dy * dy);\n        const style = getComputedStyle(node);\n        const transform = style.transform === 'none' ? '' : style.transform;\n        const opacity = +style.opacity;\n        return {\n            delay,\n            duration: is_function(duration) ? duration(d) : duration,\n            easing,\n            css: (t, u) => `\n\t\t\t\topacity: ${t * opacity};\n\t\t\t\ttransform-origin: top left;\n\t\t\t\ttransform: ${transform} translate(${u * dx}px,${u * dy}px) scale(${t + (1 - t) * dw}, ${t + (1 - t) * dh});\n\t\t\t`\n        };\n    }\n    function transition(items, counterparts, intro) {\n        return (node, params) => {\n            items.set(params.key, node);\n            return () => {\n                if (counterparts.has(params.key)) {\n                    const other_node = counterparts.get(params.key);\n                    counterparts.delete(params.key);\n                    return crossfade(other_node, node, params);\n                }\n                // if the node is disappearing altogether\n                // (i.e. wasn't claimed by the other list)\n                // then we need to supply an outro\n                items.delete(params.key);\n                return fallback && fallback(node, params, intro);\n            };\n        };\n    }\n    return [\n        transition(to_send, to_receive, false),\n        transition(to_receive, to_send, true)\n    ];\n}\n\nexport { blur, crossfade, draw, fade, fly, scale, slide };\n"], "mappings": ";;;;;;;;;;;;;AAkBA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AAAG,QAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,QAAE,KAAK,EAAE;AACb,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,EAAE,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,EAAE;AACzE,UAAE,EAAE,MAAM,EAAE,EAAE;AAAA,IACtB;AACJ,SAAO;AACX;AAEA,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,YAAY,SAAS,GAAG,UAAU,EAAE,IAAI,CAAC,GAAG;AAClG,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,IAAI,MAAM,WAAW,SAAS,KAAK,MAAM;AAC/C,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,OAAO,IAAI,IAAI,eAAe,MAAM;AAC3C,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,IAAI,MAAM,YAAY,iBAAkB,KAAK,cAAe,UAAU,IAAI,QAAQ;AAAA,EAC5F;AACJ;AACA,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,SAAO,IAAI,CAAC,GAAG;AACrE,QAAM,IAAI,CAAC,iBAAiB,IAAI,EAAE;AAClC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,OAAK,YAAY,IAAI;AAAA,EAC9B;AACJ;AACA,SAAS,IAAI,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,IAAI,GAAG,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,GAAG;AACjG,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,QAAQ,KAAK,IAAI,eAAe,CAAC;AACxC,QAAM,CAAC,QAAQ,KAAK,IAAI,eAAe,CAAC;AACxC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AAAA,gBACP,wBAAwB,IAAI,KAAK,SAAS,WAAW,IAAI,KAAK,SAAS;AAAA,cACzE,iBAAkB,KAAK;AAAA,EACjC;AACJ;AACA,SAAS,MAAM,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,OAAO,IAAI,IAAI,CAAC,GAAG;AACpF,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,UAAU,CAAC,MAAM;AACvB,QAAM,mBAAmB,SAAS,MAAM,WAAW;AACnD,QAAM,yBAAyB,WAAW,MAAM,iBAAiB;AACjE,QAAM,uBAAuB,SAAS,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,QAAQ,OAAO;AAChF,QAAM,mCAAmC,qBAAqB,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,YAAY,IAAI,EAAE,MAAM,CAAC,GAAG;AAC7G,QAAM,sBAAsB,WAAW,MAAM,UAAU,iCAAiC,KAAK;AAC7F,QAAM,oBAAoB,WAAW,MAAM,UAAU,iCAAiC,KAAK;AAC3F,QAAM,qBAAqB,WAAW,MAAM,SAAS,iCAAiC,KAAK;AAC3F,QAAM,mBAAmB,WAAW,MAAM,SAAS,iCAAiC,KAAK;AACzF,QAAM,2BAA2B,WAAW,MAAM,SAAS,iCAAiC,UAAU;AACtG,QAAM,yBAAyB,WAAW,MAAM,SAAS,iCAAiC,UAAU;AACpG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,OAAK,6BACM,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,WAC/B,qBAAqB,IAAI,oCACjB,qBAAqB,OAAO,IAAI,iCAChC,qBAAqB,OAAO,IAAI,8BACjC,qBAAqB,OAAO,IAAI,+BAChC,qBAAqB,OAAO,IAAI,6BAChC,qBAAqB,aAAa,IAAI,qCACtC,qBAAqB,aAAa,IAAI;AAAA,EACxD;AACJ;AACA,SAAS,MAAM,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,QAAQ,GAAG,UAAU,EAAE,IAAI,CAAC,GAAG;AAChG,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,kBAAkB,IAAI;AACjC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,IAAI,MAAM;AAAA,gBACR,mBAAmB,IAAK,KAAK;AAAA,cAC/B,iBAAkB,KAAK;AAAA;AAAA,EAEjC;AACJ;AACA,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,OAAO,UAAU,SAAS,WAAW,IAAI,CAAC,GAAG;AAC1E,MAAI,MAAM,KAAK,eAAe;AAC9B,QAAM,QAAQ,iBAAiB,IAAI;AACnC,MAAI,MAAM,kBAAkB,QAAQ;AAChC,WAAO,SAAS,MAAM,WAAW;AAAA,EACrC;AACA,MAAI,aAAa,QAAW;AACxB,QAAI,UAAU,QAAW;AACrB,iBAAW;AAAA,IACf,OACK;AACD,iBAAW,MAAM;AAAA,IACrB;AAAA,EACJ,WACS,OAAO,aAAa,YAAY;AACrC,eAAW,SAAS,GAAG;AAAA,EAC3B;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AAAA,uBACA;AAAA,wBACC,IAAI;AAAA;AAAA,EAExB;AACJ;AACA,SAAS,UAAU,IAAI;AACnB,MAAI,EAAE,SAAS,IAAI,IAAI,WAAW,OAAO,IAAI,CAAC,UAAU,CAAC;AACzD,QAAM,aAAa,oBAAI,IAAI;AAC3B,QAAM,UAAU,oBAAI,IAAI;AACxB,WAASA,WAAU,WAAW,MAAM,QAAQ;AACxC,UAAM,EAAE,QAAQ,GAAG,WAAW,CAAAC,OAAK,KAAK,KAAKA,EAAC,IAAI,IAAI,SAAS,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,MAAM;AAC/G,UAAM,OAAO,UAAU,sBAAsB;AAC7C,UAAM,KAAK,KAAK,sBAAsB;AACtC,UAAM,KAAK,KAAK,OAAO,GAAG;AAC1B,UAAM,KAAK,KAAK,MAAM,GAAG;AACzB,UAAM,KAAK,KAAK,QAAQ,GAAG;AAC3B,UAAM,KAAK,KAAK,SAAS,GAAG;AAC5B,UAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACrC,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,UAAM,UAAU,CAAC,MAAM;AACvB,WAAO;AAAA,MACH;AAAA,MACA,UAAU,YAAY,QAAQ,IAAI,SAAS,CAAC,IAAI;AAAA,MAChD;AAAA,MACA,KAAK,CAAC,GAAG,MAAM;AAAA,eACZ,IAAI;AAAA;AAAA,iBAEF,uBAAuB,IAAI,QAAQ,IAAI,eAAe,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK;AAAA;AAAA,IAElG;AAAA,EACJ;AACA,WAAS,WAAW,OAAO,cAAc,OAAO;AAC5C,WAAO,CAAC,MAAM,WAAW;AACrB,YAAM,IAAI,OAAO,KAAK,IAAI;AAC1B,aAAO,MAAM;AACT,YAAI,aAAa,IAAI,OAAO,GAAG,GAAG;AAC9B,gBAAM,aAAa,aAAa,IAAI,OAAO,GAAG;AAC9C,uBAAa,OAAO,OAAO,GAAG;AAC9B,iBAAOD,WAAU,YAAY,MAAM,MAAM;AAAA,QAC7C;AAIA,cAAM,OAAO,OAAO,GAAG;AACvB,eAAO,YAAY,SAAS,MAAM,QAAQ,KAAK;AAAA,MACnD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH,WAAW,SAAS,YAAY,KAAK;AAAA,IACrC,WAAW,YAAY,SAAS,IAAI;AAAA,EACxC;AACJ;", "names": ["crossfade", "d"]}