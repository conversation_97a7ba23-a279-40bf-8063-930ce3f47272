{"version": 3, "sources": ["../../dsbridge/index.js"], "sourcesContent": ["var bridge = {\n    default:this,// for typescript\n    call: function (method, args, cb) {\n        var ret = '';\n        if (typeof args == 'function') {\n            cb = args;\n            args = {};\n        }\n        var arg={data:args===undefined?null:args}\n        if (typeof cb == 'function') {\n            var cbName = 'dscb' + window.dscb++;\n            window[cbName] = cb;\n            arg['_dscbstub'] = cbName;\n        }\n        arg = JSON.stringify(arg)\n\n        //if in webview that dsBridge provided, call!\n        if(window._dsbridge){\n           ret=  _dsbridge.call(method, arg)\n        }else if(window._dswk||navigator.userAgent.indexOf(\"_dsbridge\")!=-1){\n           ret = prompt(\"_dsbridge=\" + method, arg);\n        }\n\n       return  JSON.parse(ret||'{}').data\n    },\n    register: function (name, fun, asyn) {\n        var q = asyn ? window._dsaf : window._dsf\n        if (!window._dsInit) {\n            window._dsInit = true;\n            //notify native that js apis register successfully on next event loop\n            setTimeout(function () {\n                bridge.call(\"_dsb.dsinit\");\n            }, 0)\n        }\n        if (typeof fun == \"object\") {\n            q._obs[name] = fun;\n        } else {\n            q[name] = fun\n        }\n    },\n    registerAsyn: function (name, fun) {\n        this.register(name, fun, true);\n    },\n    hasNativeMethod: function (name, type) {\n        return this.call(\"_dsb.hasNativeMethod\", {name: name, type:type||\"all\"});\n    },\n    disableJavascriptDialogBlock: function (disable) {\n        this.call(\"_dsb.disableJavascriptDialogBlock\", {\n            disable: disable !== false\n        })\n    }\n};\n\n!function () {\n    if (window._dsf) return;\n    var ob = {\n        _dsf: {\n            _obs: {}\n        },\n        _dsaf: {\n            _obs: {}\n        },\n        dscb: 0,\n        dsBridge: bridge,\n        close: function () {\n            bridge.call(\"_dsb.closePage\")\n        },\n        _handleMessageFromNative: function (info) {\n            var arg = JSON.parse(info.data);\n            var ret = {\n                id: info.callbackId,\n                complete: true\n            }\n            var f = this._dsf[info.method];\n            var af = this._dsaf[info.method]\n            var callSyn = function (f, ob) {\n                ret.data = f.apply(ob, arg)\n                bridge.call(\"_dsb.returnValue\", ret)\n            }\n            var callAsyn = function (f, ob) {\n                arg.push(function (data, complete) {\n                    ret.data = data;\n                    ret.complete = complete!==false;\n                    bridge.call(\"_dsb.returnValue\", ret)\n                })\n                f.apply(ob, arg)\n            }\n            if (f) {\n                callSyn(f, this._dsf);\n            } else if (af) {\n                callAsyn(af, this._dsaf);\n            } else {\n                //with namespace\n                var name = info.method.split('.');\n                if (name.length<2) return;\n                var method=name.pop();\n                var namespace=name.join('.')\n                var obs = this._dsf._obs;\n                var ob = obs[namespace] || {};\n                var m = ob[method];\n                if (m && typeof m == \"function\") {\n                    callSyn(m, ob);\n                    return;\n                }\n                obs = this._dsaf._obs;\n                ob = obs[namespace] || {};\n                m = ob[method];\n                if (m && typeof m == \"function\") {\n                    callAsyn(m, ob);\n                    return;\n                }\n            }\n        }\n    }\n    for (var attr in ob) {\n        window[attr] = ob[attr]\n    }\n    bridge.register(\"_hasJavascriptMethod\", function (method, tag) {\n         var name = method.split('.')\n         if(name.length<2) {\n           return !!(_dsf[name]||_dsaf[name])\n         }else{\n           // with namespace\n           var method=name.pop()\n           var namespace=name.join('.')\n           var ob=_dsf._obs[namespace]||_dsaf._obs[namespace]\n           return ob&&!!ob[method]\n         }\n    })\n}();\n\nmodule.exports = bridge;"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,SAAS;AAAA,MACT,SAAQ;AAAA,MACR,MAAM,SAAU,QAAQ,MAAM,IAAI;AAC9B,YAAI,MAAM;AACV,YAAI,OAAO,QAAQ,YAAY;AAC3B,eAAK;AACL,iBAAO,CAAC;AAAA,QACZ;AACA,YAAI,MAAI,EAAC,MAAK,SAAO,SAAU,OAAK,KAAI;AACxC,YAAI,OAAO,MAAM,YAAY;AACzB,cAAI,SAAS,SAAS,OAAO;AAC7B,iBAAO,UAAU;AACjB,cAAI,eAAe;AAAA,QACvB;AACA,cAAM,KAAK,UAAU,GAAG;AAGxB,YAAG,OAAO,WAAU;AACjB,gBAAM,UAAU,KAAK,QAAQ,GAAG;AAAA,QACnC,WAAS,OAAO,SAAO,UAAU,UAAU,QAAQ,WAAW,KAAG,IAAG;AACjE,gBAAM,OAAO,eAAe,QAAQ,GAAG;AAAA,QAC1C;AAED,eAAQ,KAAK,MAAM,OAAK,IAAI,EAAE;AAAA,MACjC;AAAA,MACA,UAAU,SAAU,MAAM,KAAK,MAAM;AACjC,YAAI,IAAI,OAAO,OAAO,QAAQ,OAAO;AACrC,YAAI,CAAC,OAAO,SAAS;AACjB,iBAAO,UAAU;AAEjB,qBAAW,WAAY;AACnB,mBAAO,KAAK,aAAa;AAAA,UAC7B,GAAG,CAAC;AAAA,QACR;AACA,YAAI,OAAO,OAAO,UAAU;AACxB,YAAE,KAAK,QAAQ;AAAA,QACnB,OAAO;AACH,YAAE,QAAQ;AAAA,QACd;AAAA,MACJ;AAAA,MACA,cAAc,SAAU,MAAM,KAAK;AAC/B,aAAK,SAAS,MAAM,KAAK,IAAI;AAAA,MACjC;AAAA,MACA,iBAAiB,SAAU,MAAM,MAAM;AACnC,eAAO,KAAK,KAAK,wBAAwB,EAAC,MAAY,MAAK,QAAM,MAAK,CAAC;AAAA,MAC3E;AAAA,MACA,8BAA8B,SAAU,SAAS;AAC7C,aAAK,KAAK,qCAAqC;AAAA,UAC3C,SAAS,YAAY;AAAA,QACzB,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,KAAC,WAAY;AACT,UAAI,OAAO;AAAM;AACjB,UAAI,KAAK;AAAA,QACL,MAAM;AAAA,UACF,MAAM,CAAC;AAAA,QACX;AAAA,QACA,OAAO;AAAA,UACH,MAAM,CAAC;AAAA,QACX;AAAA,QACA,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO,WAAY;AACf,iBAAO,KAAK,gBAAgB;AAAA,QAChC;AAAA,QACA,0BAA0B,SAAU,MAAM;AACtC,cAAI,MAAM,KAAK,MAAM,KAAK,IAAI;AAC9B,cAAI,MAAM;AAAA,YACN,IAAI,KAAK;AAAA,YACT,UAAU;AAAA,UACd;AACA,cAAI,IAAI,KAAK,KAAK,KAAK;AACvB,cAAI,KAAK,KAAK,MAAM,KAAK;AACzB,cAAI,UAAU,SAAUA,IAAGC,KAAI;AAC3B,gBAAI,OAAOD,GAAE,MAAMC,KAAI,GAAG;AAC1B,mBAAO,KAAK,oBAAoB,GAAG;AAAA,UACvC;AACA,cAAI,WAAW,SAAUD,IAAGC,KAAI;AAC5B,gBAAI,KAAK,SAAU,MAAM,UAAU;AAC/B,kBAAI,OAAO;AACX,kBAAI,WAAW,aAAW;AAC1B,qBAAO,KAAK,oBAAoB,GAAG;AAAA,YACvC,CAAC;AACD,YAAAD,GAAE,MAAMC,KAAI,GAAG;AAAA,UACnB;AACA,cAAI,GAAG;AACH,oBAAQ,GAAG,KAAK,IAAI;AAAA,UACxB,WAAW,IAAI;AACX,qBAAS,IAAI,KAAK,KAAK;AAAA,UAC3B,OAAO;AAEH,gBAAI,OAAO,KAAK,OAAO,MAAM,GAAG;AAChC,gBAAI,KAAK,SAAO;AAAG;AACnB,gBAAI,SAAO,KAAK,IAAI;AACpB,gBAAI,YAAU,KAAK,KAAK,GAAG;AAC3B,gBAAI,MAAM,KAAK,KAAK;AACpB,gBAAIA,MAAK,IAAI,cAAc,CAAC;AAC5B,gBAAI,IAAIA,IAAG;AACX,gBAAI,KAAK,OAAO,KAAK,YAAY;AAC7B,sBAAQ,GAAGA,GAAE;AACb;AAAA,YACJ;AACA,kBAAM,KAAK,MAAM;AACjB,YAAAA,MAAK,IAAI,cAAc,CAAC;AACxB,gBAAIA,IAAG;AACP,gBAAI,KAAK,OAAO,KAAK,YAAY;AAC7B,uBAAS,GAAGA,GAAE;AACd;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,QAAQ,IAAI;AACjB,eAAO,QAAQ,GAAG;AAAA,MACtB;AACA,aAAO,SAAS,wBAAwB,SAAU,QAAQ,KAAK;AAC1D,YAAI,OAAO,OAAO,MAAM,GAAG;AAC3B,YAAG,KAAK,SAAO,GAAG;AAChB,iBAAO,CAAC,EAAE,KAAK,SAAO,MAAM;AAAA,QAC9B,OAAK;AAEH,cAAI,SAAO,KAAK,IAAI;AACpB,cAAI,YAAU,KAAK,KAAK,GAAG;AAC3B,cAAIA,MAAG,KAAK,KAAK,cAAY,MAAM,KAAK;AACxC,iBAAOA,OAAI,CAAC,CAACA,IAAG;AAAA,QAClB;AAAA,MACL,CAAC;AAAA,IACL,EAAE;AAEF,WAAO,UAAU;AAAA;AAAA;", "names": ["f", "ob"]}